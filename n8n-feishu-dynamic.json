=({
  "receive_id": "ou_ad3229f06374881e2c63cb337c8f9d63",
  "msg_type": "interactive",
  "content": JSON.stringify({
    "type": "template",
    "data": {
      "template_id": $json.template_id || "你的默认模板ID",
      "template_version_name": "1.0.0",
      "template_variable": {
        "news_title": $json.title || "默认标题",
        "news_content": $json.content || "默认内容",
        "news_image": $json.image_url || ""
      }
    }
  })
})